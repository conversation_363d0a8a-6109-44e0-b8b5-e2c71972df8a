package com.lq.rm30_ak;

import java.io.*;

public class d {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        String s = br.readLine();
        int n = s.length();

        long cntQQ = 0, cntQLQ = 0;
        for (int i = 0; i < n - 1; i++) {
            if (s.charAt(i) == 'Q' && s.charAt(i+1) == 'Q') {
                cntQQ++;
            }
        }
        for (int i = 1; i < n - 1; i++) {
            if (s.charAt(i-1)=='Q' && s.charAt(i)=='L' && s.charAt(i+1)=='Q') {
                cntQLQ++;
            }
        }

        long ans;
        if (cntQQ > 0) {
            ans = (long)n - cntQQ - cntQLQ;
        } else {
            ans = (long)n - 1 - cntQLQ;
        }

        System.out.println(ans);
    }
}
