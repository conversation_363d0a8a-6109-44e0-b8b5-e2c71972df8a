package com.lq.rm30_ak;

import java.io.*;
import java.util.StringTokenizer;

public class f {

    static long gcd(long a, long b) {
        return b == 0 ? a : gcd(b, a % b);
    }

    static long lcm(long a, long b) {
        return a / gcd(a, b) * b;
    }

    static long count(long d, long x, long y, long z, long lxy, long lxz, long lyz, long lxyz) {
        return d / x + d / y + d / z
                - d / lxy - d / lxz - d / lyz
                + d / lxyz;
    }

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        PrintWriter out = new PrintWriter(System.out);

        int T = Integer.parseInt(br.readLine());
        while (T-- > 0) {
            StringTokenizer st = new StringTokenizer(br.readLine());
            long x = Long.parseLong(st.nextToken());
            long y = Long.parseLong(st.nextToken());
            long z = Long.parseLong(st.nextToken());
            long k = Long.parseLong(st.nextToken());

            long lxy = lcm(x, y);
            long lxz = lcm(x, z);
            long lyz = lcm(y, z);
            long lxyz = lcm(lxy, z);

            long l = 1, r = (long) 1e18, ans = -1;
            while (l <= r) {
                long mid = (l + r) / 2;
                long cnt = count(mid, x, y, z, lxy, lxz, lyz, lxyz);

                if (cnt >= k) {
                    ans = mid;
                    r = mid - 1;
                } else {
                    l = mid + 1;
                }
            }

            out.println(ans);
        }

        out.flush();
        out.close();
        br.close();
    }
}
