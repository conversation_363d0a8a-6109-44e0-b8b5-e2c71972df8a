package com.lq.rm30_ak;

import java.io.*;

public class b {

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));
        in.nextToken(); int n = (int) in.nval;
        in.nextToken(); int m = (int) in.nval;
        int x = n * 2 - 2;
        int t = x;
        int ans = -1;
        for (int i = 1; i < n; i++, t = t - 2) {
            int j = i;
            int tmp = t;
            while (j <= m) {
                if (j == m) {
                    ans = i;
                    break;
                }
                j += tmp;
                tmp = x - tmp;
            }
            if (ans != -1) {
                break;
            }
        }
        out.println(ans);
        out.flush();
        out.close();
        br.close();
    }
}