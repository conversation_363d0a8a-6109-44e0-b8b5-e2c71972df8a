package com.lq.rm30_ak;

import java.io.*;
import java.util.Arrays;

public class e {

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));
        in.nextToken(); int n = (int) in.nval;
        in.nextToken(); int k = (int) in.nval;
        int[] v = new int[n];
        for (int i = 0; i < n; i++) {
            in.nextToken();
            v[i] = (int) in.nval;
        }
        Arrays.sort(v);
        int ans = 0;
        for (int i = n - 1, power; i >= 0; ) {
            power = 0;
            int count = 1;
            while (i >= 0 && power < k) {
                power = v[i] * count;
                i--;
                count++;
            }
            if (power >= k) {
                ans++;
            }
        }
        out.println(ans);
        out.flush();
        out.close();
        br.close();
    }
}