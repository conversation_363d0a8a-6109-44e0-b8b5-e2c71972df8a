package com.lq.rm30_ak;

import java.io.*;

public class a {

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));
        out.println(1 + 3 + 5 + 7);
        out.flush();
        out.close();
        br.close();
    }
}