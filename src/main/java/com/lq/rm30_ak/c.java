package com.lq.rm30_ak;

import java.io.*;
import java.util.Arrays;

public class c {

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));
        in.nextToken(); int n = (int) in.nval;
        in.nextToken(); int m = (int) in.nval;
        int[] arr = new int[n];
        for (int i = 0; i < n; i++) {
            in.nextToken();
            arr[i] = (int) in.nval;
        }
        Arrays.sort(arr);
        int ans = -1;
        for (int i = 1; i < n; i++) {
            ans = Math.max(ans, arr[i] - arr[i - 1]);
        }
        out.println(m - ans);
        out.flush();
        out.close();
        br.close();
    }
}