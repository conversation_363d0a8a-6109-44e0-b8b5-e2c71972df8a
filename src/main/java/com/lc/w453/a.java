package com.lc.w453;

import java.io.*;
import java.util.Arrays;

public class a {
    public static void main(String[] args) throws IOException {
    	if (canMakeEqual(new int[] {1,-1,1,-1,1,-1,1,-1,-1,-1}, 4)) {
    		System.out.println("yes");
    	} else {
            System.out.println("no");
        }
    }
    
        public static boolean canMakeEqual(int[] nums, int k) {
            if (nums.length < 2) return true;
            boolean ans;

            ans = f(nums, -1, k) || f(nums, 1, k);
            return ans;
        }

        public static boolean f(int[] nums, int x, int k) {
            int[] arr = Arrays.copyOf(nums, nums.length);
            int res = 0;
            int n = arr.length;
            for (int i = 0; i < n - 1; i++) {
                if (arr[i] == x) {
                    res++;
                    arr[i] = -arr[i];
                    arr[i + 1] = -arr[i + 1];
                } 
            }
            if (arr[n - 1] != arr[n - 2]) {
                res = Integer.MAX_VALUE;
            }
            return res <= k;
    }
}
