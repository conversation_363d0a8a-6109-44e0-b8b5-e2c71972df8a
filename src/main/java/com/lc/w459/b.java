package com.lc.w459;

import java.io.IOException;
import java.util.*;

public class b {

    public static void main(String[] args) throws IOException {
        System.out.println(countTrapezoids(new int[][] { {1, 0}, {2, 0}, {3, 0}, {2, 2}, {3, 2} }) );
    }

    public static int countTrapezoids(int[][] points) {
        final int MOD = 1_000_000_007;
        int n = points.length;
        // 1) 统计每个 y 上的点数
        Map<Integer, Integer> freq = new HashMap<>();
        for (int[] p : points) {
            freq.put(p[1], freq.getOrDefault(p[1], 0) + 1);
        }
        // 2) 预计算 nC2
        long totalN2 = comb2(n, MOD);
        // 3) 遍历每一行，累加 A = Σ C(f_i,2) * C(n - f_i,2)
        long A = 0;
        long sumCi2 = 0;        // 统计 Σ C(f_i,2) 用于后面算 B
        long sumCi2Square = 0;  // Σ C(f_i,2)^2
        for (int fi : freq.values()) {
            long ci2 = comb2(fi, MOD);
            sumCi2 = (sumCi2 + ci2) % MOD;
            sumCi2Square = (sumCi2Square + ci2 * ci2) % MOD;
            // 从其它点中选 2 个：C(n - fi, 2)
            long other2 = comb2(n - fi, MOD);
            A = (A + ci2 * other2) % MOD;
        }
        // 4) 计算 B = Σ_{i<j} C(f_i,2)*C(f_j,2)
        //    = ( (Σ ci2)^2 - Σ (ci2^2) ) / 2
        long B = ((sumCi2 * sumCi2 % MOD - sumCi2Square + MOD) % MOD) *
                modInv(2, MOD) % MOD;
        // 5) 答案 = A - B (取模)
        long ans = (A - B + MOD) % MOD;
        return (int) ans;
    }

    // 计算 xC2 = x*(x-1)/2 mod MOD
    private static long comb2(long x, int MOD) {
        if (x < 2) return 0;
        return (x * (x - 1) / 2) % MOD;
    }

    // 快速幂求逆元：a^{-1} mod p
    private static long modInv(long a, int p) {
        // p 是质数，这里用 a^(p-2) mod p
        long res = 1, e = p - 2;
        a %= p;
        while (e > 0) {
            if ((e & 1) == 1) res = (res * a) % p;
            a = (a * a) % p;
            e >>= 1;
        }
        return res;
    }
}
