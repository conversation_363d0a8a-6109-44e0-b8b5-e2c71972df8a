package com.lc.w459;

import java.io.IOException;
import java.util.*;

public class a {

    public static void main(String[] args) throws IOException {
        System.out.println();
    }

    public boolean checkDivisibility(int n) {
        int x = nsum(n) + nmut(n);
        if (n % x == 0) {
            return true;
        } else {
            return false;
        }
    }

    public static int nsum(int n) {
        int sum = 0;
        while (n > 0) {
            sum += n % 10;
            n /= 10;
        }
        return sum;
    }

    public static int nmut(int n) {
        int mut = 1;
        while (n > 0) {
            mut *= n % 10;
            n /= 10;
        }
        return mut;
    }
}
