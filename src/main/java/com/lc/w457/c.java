package com.lc.w457;

import java.io.IOException;
import java.util.*;

public class c {

    public static void main(String[] args) throws IOException {
        System.out.println();
    }

    public int minTime(int n, int[][] edges, int k) {
        Arrays.sort(edges, (a, b) -> b[2] - a[2]);

        UnionFind u = new UnionFind(n);
        for (int[] e : edges) {
            u.union(e[0], e[1]);
            if (u.cc < k) {
                return e[2];
            }
        }
        return 0;
    }
}

// 并查集
class UnionFind {
    private final int[] fa;
    public int cc;

    UnionFind(int n) {
        fa = new int[n];
        for (int i = 0; i < n; i++) {
            fa[i] = i;
        }
        cc = n;
    }

    public int find(int x) {
        if (fa[x] != x) {
            fa[x] = find(fa[x]);
        }
        return fa[x];
    }

    public void union(int u, int v) {
        int x = find(u);
        int y = find(v);
        if (x == y) {
            return;
        }
        fa[x] = y;
        cc--;
    }
}
