package com.lc.w457;

import java.io.IOException;
import java.util.*;

public class a {

    public static void main(String[] args) throws IOException {
        System.out.println();
    }

    public List<String> validateCoupons(String[] code, String[] businessLine, boolean[] isActive) {
        int sum = 0;
        for (int i = 0; i < isActive.length; i++) {
            sum += isActive[i] ? 1 : 0;
        }
        List<String[]> ans = new ArrayList<>();
        // 1. 定义排序顺序
        List<String> order = Arrays.asList("electronics", "grocery", "pharmacy", "restaurant");
        for (int i = 0; i < code.length; i++) {
            if (isActive[i] && isVild(code[i])  && order.contains(businessLine[i])) {
                ans.add(new String[] { code[i], businessLine[i] });
            }
        }

        // 2. 将顺序映射成权重
        Map<String, Integer> orderMap = new HashMap<>();
        for (int i = 0; i < order.size(); i++) {
            orderMap.put(order.get(i), i);
        }

        ans.sort((o1, o2) -> {
            int cmp = Integer.compare(
                    orderMap.getOrDefault(o1[1], Integer.MAX_VALUE),
                    orderMap.getOrDefault(o2[1], Integer.MAX_VALUE)
            );
            if (cmp != 0) {
                return cmp;
            }
            return o1[0].compareTo(o2[0]);
        });
        List<String> result = new ArrayList<>();
        for (int i = 0; i < ans.size(); i++) {
            result.add(ans.get(i)[0]);
        }
        return result;
    }

    private boolean isVild(String s) {
        if (s.length() == 0) return false;
        int count = 0;
        for (int i = 0; i < s.length(); i++) {
            if ((s.charAt(i) <= 'Z' && s.charAt(i) >= 'A')
                    || (s.charAt(i) <= 'z' && s.charAt(i) >= 'a')
                    || (s.charAt(i) <= '9' && s.charAt(i) >= '0')
                    || (s.charAt(i) == '_')) {
                count++;
            }
        }
        return count == s.length();
    }
}
