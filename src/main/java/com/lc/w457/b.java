package com.lc.w457;

import java.io.IOException;
import java.util.*;

public class b {

    public static void main(String[] args) throws IOException {
        System.out.println();
    }

    public int[] processQueries(int c, int[][] connections, int[][] queries) {
        int[] father = new int[c + 1];
        boolean[] online = new boolean[c + 1];
        Queue<Integer>[] q = new PriorityQueue[c + 1];

        for (int i = 0; i < q.length; i++) q[i] = new PriorityQueue<>();

        Arrays.fill(online, true);

        for (int i = 1; i <= c; i++) {
            father[i] = i;
        }

        for (int i = 0; i < connections.length; i++) {
            int x = find(father, connections[i][0]);
            int y = find(father, connections[i][1]);
            if (x != y) {
                father[y] = x;
            }
        }

        for (int i = 1; i <= c; i++) {
            int a = find(father, i);
            q[a].offer(i);
        }

        List<Integer> lists = new ArrayList<>();

        for (int i = 0; i < queries.length; i++) {
            int x = find(father, queries[i][1]);
            int t = queries[i][0];
            if (t == 1) {
                if (online[queries[i][1]]) {
                    lists.add(queries[i][1]);
                } else {
                    while (!q[x].isEmpty() && !online[q[x].peek()]) {
                        q[x].poll();
                    }
                    if (q[x].isEmpty()) {
                        lists.add(-1);
                    } else {
                        lists.add(q[x].peek());
                    }
                }
            } else {
                online[queries[i][1]] = false;
            }
        }
        return lists.stream().mapToInt(Integer::intValue).toArray();
    }

    public int find(int[] father, int x) {
        if (father[x] != x) father[x] = find(father, father[x]);
        return father[x];
    }
}
