package com.lc.w455;

import java.io.IOException;
import java.util.*;

public class b {

    public static void main(String[] args) throws IOException {
        System.out.println(Arrays.toString(findCoins(new int[]{1, 2, 3, 4, 5, 4}).toArray()));
    }

    public static List<Integer> findCoins(int[] numWays) {
        int n = numWays.length;
        int[] dp = new int[n + 1];
        dp[0] = 1;
        List<Integer> ans = new ArrayList<>();
        for (int i = 1; i <= n; i++) {
            int ways = numWays[i - 1];
            if (ways == dp[i]) {
                continue;
            }
            if (ways - 1 != dp[i]) {
                return List.of();
            }
            ans.add(i);
            for (int j = i; j <= n; j++) {
                dp[j] += dp[j - i];
            }
        }
        return ans;
    }
}
