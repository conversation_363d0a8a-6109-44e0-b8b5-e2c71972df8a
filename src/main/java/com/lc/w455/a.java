package com.lc.w455;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class a {

    public static void main(String[] args) throws IOException {
        System.out.println(checkPrimeFrequency(new int[]{1,2,3,4,5,4}) ? "true" : "false");
    }

    public static boolean checkPrimeFrequency(int[] nums) {
        Map<Integer, Integer> map = new HashMap<>();
        for (int a : nums) {
            map.put(a, map.getOrDefault(a , 0) + 1);
        }
        for (Integer key : map.keySet()) {
            Integer val = map.get(key);
            if (check(val)) {
                return true;
            }
        }
        return false;
    }

    public static boolean check(int a) {
        if (a <= 1) {
            return false;
        }
        if (a == 2) {
            return true;
        }
        if (a % 2 == 0) {
            return false;
        }

        for (int i = 3; i * i <= a; i += 2) {
            if (a % i == 0) {
                return false;
            }
        }
        return true;
    }
}
