package com.cf.r1017;

import java.io.*;

public class b {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken(); int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken(); int n = (int) in.nval;
            in.nextToken(); int m = (int) in.nval;
            in.nextToken(); int l = (int) in.nval;
            in.nextToken(); int r = (int) in.nval;
            int ll = 0, rr = 0;
            while ((ll >= l || rr <= r) && m-- > 0) {
                if (ll - 1 >= l) {
                    ll--;
                } else if (rr + 1 <= r) {
                    rr++;
                }
            }
            out.println(ll + " " + rr);
        }
        out.close();
        out.close();
        br.close();
    }
}
