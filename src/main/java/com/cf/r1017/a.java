package com.cf.r1017;

import java.io.*;

public class a {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        PrintWriter out = new PrintWriter(System.out);
        int t = Integer.parseInt(br.readLine());
        while (t-- > 0) {
            String[] s = br.readLine().split(" ");
            StringBuilder ans = new StringBuilder();
            for (String string : s) {
                ans.append(string.charAt(0));
            }
            out.println(ans);
        }
        out.close();
        out.close();
        br.close();
    }
}
