package com.cf.r1017;

import java.io.*;
import java.util.*;

public class g {

    static long sum, s1, s2, h1, h2;
    static Deque<Integer> a;

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken(); int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken(); int q = (int) in.nval;
            sum = s1 = s2 = 0;
            h1 = h2 = (long)q * 3;
            a = new LinkedList<>();
            int dir = 0;

            while (q-- > 0) {
                in.nextToken(); int s = (int) in.nval;
                if (s == 3) {
                    in.nextToken();
                    int k = (int) in.nval;
                    put(dir, k);
                } else if (s == 2) {
                    dir ^= 1;
                } else{
                    if (dir == 0) {
                        Integer x = a.pollLast();
                        if (x != null) {
                            sum -= x;
                            s1 -= (h1 + a.size()) * x;
                            s2 -= h2 * x;
                            h2++;
                            put(1, x);
                        }
                    } else {
                        Integer x = a.pollFirst();
                        if (x != null) {
                            sum -= x;
                            s2 -= (h2 + a.size()) * x;
                            s1 -= h1 * x;
                            h1++;
                            put(0, x);
                        }
                    }
                }
                long ans = (dir == 0)
                        ? s1 - sum * (h1 - 1)
                        : s2 - sum * (h2 - 1);
                out.println(ans);
            }
        }
        out.close();
        out.close();
        br.close();
    }

    public static void put(int dir, int k) {
        sum += k;
        if (dir == 0) {
            s1 += (h1 + a.size()) * k;
            s2 += (--h2) * k;
            a.addLast(k);
        } else {
            s1 += (--h1) * k;
            s2 += (h2 + a.size()) * k;
            a.addFirst(k);
        }
    }
}
