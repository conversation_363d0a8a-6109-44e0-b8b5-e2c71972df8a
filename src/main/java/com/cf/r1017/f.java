package com.cf.r1017;

import java.io.*;
import java.util.Arrays;
import java.util.Iterator;
import java.util.TreeSet;

public class f {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken(); int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken(); int n = (int) in.nval;
            in.nextToken(); int m = (int) in.nval;
            in.nextToken(); int k = (int) in.nval;
            int[][] ans = new int[n][m];
            if (m % k != 0) {
                int cur = 0;
                for (int i = 0; i < n; i++) {
                    for (int j = 0; j < m; j++) {
                        ans[i][j] = (cur++) % k + 1;
                    }
                }
            } else {
                for (int i = 0; i < n; i++) {
                    for (int j = 0; j < m; j++) {
                        if (i % 2== 0) {
                            ans[i][j] = (j % k) + 1;
                        } else {
                            ans[i][j] = (j + 1) % k + 1;
                        }
                    }
                }
            }

            for (int i = 0; i < n; i++) {
                for (int j = 0; j < m; j++) {
                    out.print(ans[i][j] + " ");
                }
                out.println();
            }
        }
        out.close();
        out.close();
        br.close();
    }

}
