package com.cf.r1017;

import java.io.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class e {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken(); int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken(); int n = (int) in.nval;
            int[] arr = new int[n];
            for (int i = 0; i < n; i++) {
                in.nextToken();
                arr[i] = (int) in.nval;
            }
            long[] cnt1 = new long[30];
            for (int x : arr) {
                for (int b = 0; b < 30; b++) {
                    if (((x >>> b) & 1) == 1) {
                        cnt1[b]++;
                    }
                }
            }

            long[] cnt0 = new long[30];
            for (int b = 0; b < 30; b++) {
                cnt0[b] = n - cnt1[b];
            }

            long ans = 0;
            for (int x : arr) {
                long sum = 0;
                for (int b = 0; b < 30; b++) {
                    long bit = 1L << b;
                    if (((x >>> b) & 1) == 1) {
                        sum += cnt0[b] * bit;
                    } else {
                        sum += cnt1[b] * bit;
                    }
                }
                if (sum > ans) ans = sum;
            }
            out.println(ans);
        }
        out.close();
        out.close();
        br.close();
    }

}
