package com.cf.r1017;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

public class d {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        PrintWriter out = new PrintWriter(System.out);
        int t = Integer.parseInt(br.readLine());
        while (t-- > 0) {
            String p = br.readLine().trim();
            String s = br.readLine().trim();
            out.println(check(p, s) ? "YES" : "NO");
        }
        out.close();
        out.close();
        br.close();
    }

    // LLRLRLRRL
    // LLLRLRRLLRRRL
    private static boolean check(String p, String s) {
        List<Character> gp = new ArrayList<Character>();
        List<Integer> cntp = new ArrayList<Integer>();
        for (int i = 0; i < p.length();) {
            char c = p.charAt(i);
            int j = i;
            while (j < p.length() && p.charAt(j) == c) j++;
            gp.add(c);
            cntp.add(j - i);
            i = j;
        }

        List<Character> gs = new ArrayList<Character>();
        List<Integer> cnts = new ArrayList<Integer>();
        for (int i = 0; i < s.length();) {
            char c = s.charAt(i);
            int j = i;
            while (j < s.length() && s.charAt(j) == c) j++;
            gs.add(c);
            cnts.add(j - i);
            i = j;
        }

        if (gp.size() != gs.size()) return false;
        for (int k = 0; k < gp.size(); k++) {
            if (!gp.get(k).equals(gs.get(k))) return false;
            int x = cntp.get(k), y = cnts.get(k);
            if (y < x || y > 2 * x) return false;
        }
        return true;
    }

}
