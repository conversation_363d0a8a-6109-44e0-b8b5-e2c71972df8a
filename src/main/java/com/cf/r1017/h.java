package com.cf.r1017;

import java.io.*;
import java.util.ArrayList;
import java.util.Collections;

public class h {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken(); int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken(); int n = (int) in.nval;
            in.nextToken(); int q = (int) in.nval;
            int[] arr = new int[n];
            for (int i = 0; i < n; i++) {
                in.nextToken();
                arr[i] = (int) in.nval;
            }
            while (q-- > 0) {
                in.nextToken(); int k = (int) in.nval;
                in.nextToken(); int l = (int) in.nval;
                in.nextToken(); int r = (int) in.nval;
                out.println(compute(k, arr, l - 1, r - 1));
            }
        }
        out.flush();
        out.close();
        br.close();
    }

    public static long compute(int k, int[] a, int l, int r) {
        long ans = 0;
        for (int i = l; i <= r; i++) {
            while (a[i] > 1 && (k % a[i]) == 0) {
                k /= a[i];
            }
            ans += k;
        }
        return ans;
    }
}
