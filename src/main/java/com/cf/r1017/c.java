package com.cf.r1017;

import java.io.*;

public class c {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken(); int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken(); int n = (int) in.nval;
            int[][] grid = new int[n + 1][n + 1];
            StringBuilder sb = new StringBuilder();
            for (int i = 1; i <= n; i++) {
                for (int j = 1; j <= n; j++) {
                    in.nextToken(); grid[i][j] = (int) in.nval;
                }
            }

            boolean[] used = new boolean[2 * n + 1];
            boolean[] used2 = new boolean[2 * n + 1];
            for (int i = 1; i <= n; i++) {
                for (int j = 1; j <= n; j++) {
                    if (!used[i + j]) {
                        used[i + j] = true;
                        used2[grid[i][j]] = true;
                        sb.append(grid[i][j]).append(" ");
                    }
                }
            }
            StringBuilder ans = new StringBuilder(sb.toString());
            for (int i = 1; i < n * 2 + 1; i++) {
                if (!used2[i]) {
                    ans.insert(0, i + " ");
                }
            }

            out.println(ans);
        }
        out.close();
        out.close();
        br.close();
    }
}
