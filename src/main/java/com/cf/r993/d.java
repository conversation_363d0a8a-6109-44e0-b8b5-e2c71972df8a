package com.cf.r993;

import java.io.*;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class d {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken();
        int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken(); int n = (int) in.nval;
            int[] a = new int[n];
            for (int i = 0; i < n; i++) {
                in.nextToken(); a[i] = (int) in.nval;
            }
            int[] b = new int[n];
            Map<Integer, Integer> map = new HashMap<>();
            int max_size = 0; // 最大众数个数
            int need = -1;
            int size;
            Set<Integer> set = new HashSet<>(); // 当前众数表；
            for (int i = 0; i < n; i++) {
                need = a[i];
                size = map.getOrDefault(need, 0);
                int x;
                if (i == n - 1) {
                    x = a[i];
                } else {
                    x = a[i + 1];
                }
                if (max_size > 0) {
                    if (size == max_size) {
                        if (set.contains(need)) {
                            b[i] = x;
                            map.put(need, map.getOrDefault(need, 0) + 1);
                        }
                        max_size++;
                        map.put(need, map.getOrDefault(need, 0) + 1);
                        b[i] = x;
                    } else if (size < max_size) {
                        map.put(need, map.getOrDefault(need, 0) + 1);
                        b[i] = need;
                        set.add(need);
                    }
                } else {
                    map.put(need, 1);
                    max_size = 1;
                    b[i] = need;
                    set.add(need);
                }
            }
            for (int i = 0; i < n; i++) {
                out.print(b[i] + " ");
            }
            out.println();
        }
        out.close();
        out.close();
        br.close();
    }
}
