package com.cf.r993;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.Collections;

public class b {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        PrintWriter out = new PrintWriter(System.out);
        int t = Integer.parseInt(br.readLine());
        while (t-- > 0) {
            String a = br.readLine();
            int n = a.length();
            char[] s = a.toCharArray();
            for (int i = 0; i < n; i++) {
                if (s[i] == 'p') {
                    s[i] = 'q';
                } else if (s[i] == 'q') {
                    s[i] = 'p';
                }
            }
            StringBuilder ans = new StringBuilder();
            for (int i = s.length - 1; i >= 0; i--) {
                ans.append(s[i]);
            }
            out.println(ans);

        }
        out.close();
        out.close();
        br.close();
    }
}
