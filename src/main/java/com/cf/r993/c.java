package com.cf.r993;

import java.io.*;
import java.util.Arrays;

public class c {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken();
        int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken(); int m = (int) in.nval;
            in.nextToken(); int a = (int) in.nval;
            in.nextToken(); int b = (int) in.nval;
            in.nextToken(); int c = (int) in.nval;
            int ans;
            if (m >= a && m >= b) {
                ans = a + b + Math.min(c, 2 * m - a - b);
            } else if (m >= a) {
                ans = a + m + Math.min(c, m - a);
            } else {
                ans = b + m + Math.min(c, m - b);
            }
            out.println(ans);
        }
        out.close();
        out.close();
        br.close();
    }
}
