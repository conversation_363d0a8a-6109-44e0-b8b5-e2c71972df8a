package com.cf.r1038;

import java.io.*;

public class b {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken();
        int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken();
            int n = (int) in.nval;
            int[][] start = new int[n][2];
            int[][] end = new int[n][2];
            for (int i = 0; i < n; i++) {
                in.nextToken();
                start[i][0] = (int) in.nval;
                in.nextToken();
                start[i][1] = (int) in.nval;
                in.nextToken();
                end[i][0] = (int) in.nval;
                in.nextToken();
                end[i][1] = (int) in.nval;
            }
            int ans = 0;
            int extra0 = 0;
            int extar1 = 0;
            for (int i = 0; i < n; i++) {
                int[] s = start[i];
                int[] e = end[i];
                extra0 += s[0] > e[0] ? 1 : 0;
                extar1 += s[1] > e[1] ? 1 : 0;
                if (s[1] > e[1] && (s[0] > 0 && e[0] > 0)) {
                    ans++;
                }
            }
            out.println(ans + extra0 + extar1);
        }
        out.close();
        out.close();
        br.close();
    }

}