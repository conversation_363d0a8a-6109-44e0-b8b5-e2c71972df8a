package com.cf.r1038;

import java.io.*;

public class a {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken();
        int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken();
            int n = (int) in.nval;
            in.nextToken();
            int m = (int) in.nval;
            if ((n <= 2 && m <= 2) || n == 1 || m == 1) {
                out.println("NO");
            } else {
                out.println("YES");
            }
        }
        out.close();
        out.close();
        br.close();
    }

}