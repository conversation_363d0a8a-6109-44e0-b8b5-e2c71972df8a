package com.cf.r1039;

import java.io.*;
import java.util.Arrays;

public class a {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken();
        int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken();
            int n = (int) in.nval;
            in.nextToken();
            int c = (int) in.nval;
            long[] a = new long[n];
            for (int i = 0; i < n; i++) {
                in.nextToken();
                a[i] = (long) in.nval;
            }
            Arrays.sort(a);
            int l = n - 1;
            int ans =  0;
            boolean[] visited = new boolean[n];
            while (l >= 0) {
                int x = select(a, c, visited);
                if (x != -1) {
                    ans++;
                    visited[x] = true;
                }
                l = x;
                mut2(a);
            }
            out.println(n - ans);
        }
        out.close();
        out.close();
        br.close();
    }

    private static int select(long[] a, int c, boolean[] visited) {
        for (int i = a.length - 1; i >= 0; i--) {
            if (!visited[i] && a[i] < c) {
                return i;
            }
        }
        return -1;
    }

    public static void mut2(long[] a) {
        for (int i = 0; i < a.length; i++) {
            a[i] *= 2;
        }
    }

}