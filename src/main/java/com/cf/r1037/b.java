package com.cf.r1037;

import java.io.*;

public class b {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken();
        int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken(); int n = (int) in.nval;
            in.nextToken(); int k = (int) in.nval;
            int[] a = new int[n];
            for (int i = 0; i < n; i++) {
                in.nextToken();
                a[i] = (int) in.nval;
            }
            int ans = 0;
            int cnt = 0;
            for (int i = 0; i < k && i < n; i++) {
                cnt += a[i];
            }
            int i = 0;
            while (i <= n - k) {
                if (cnt == 0) {
                    ans++;
                    i += k + 1;
                    cnt = 0;
                    for (int j = i; j < i + k && j < n; j++) {
                        cnt += a[j];
                    }
                } else {
                    cnt -= a[i];
                    i++;
                    if (i + k - 1 < n) {
                        cnt += a[i + k - 1];
                    }
                }
            }
            out.println(ans);
        }
        out.close();
        out.close();
        br.close();
    }

}