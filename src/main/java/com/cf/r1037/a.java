package com.cf.r1037;

import java.io.*;

public class a {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken();
        int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken();
            int x = (int) in.nval;
            int ans = Integer.MAX_VALUE;
            while (x > 0) {
                int n = x % 10;
                ans = Math.min(ans, n);
                x /= 10;
            }
            out.println(ans);
        }
        out.close();
        out.close();
        br.close();
    }

}