package com.cf.r1037;

import java.io.*;
import java.util.*;

public class d {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken();
        int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken(); int n = (int) in.nval;
            in.nextToken(); long k = (long) in.nval - 1;

            int[] l = new int[n];
            int[] r = new int[n];
            long[] real = new long[n];
            for (int i = 0; i < n; i++) {
                in.nextToken(); l[i] = (int) in.nval;
                in.nextToken(); r[i] = (int) in.nval;
                in.nextToken(); real[i] = (long) l[i] * r[i];
            }

            Integer[] order = new Integer[n];
            for (int i = 0; i < n; i++) order[i] = i;
            Arrays.sort(order, Comparator.comparingInt(i -> l[i]));

            boolean[] used = new boolean[n];
            PriorityQueue<long[]> pq = new PriorityQueue<>((a, b) -> Long.compare(b[0], a[0]));
            Deque<Integer> queue = new ArrayDeque<>();

            long answer = k;
            int idx = 0;

            // 初始化：所有 l_i <= k
            while (idx < n && l[order[idx]] <= k) {
                queue.addLast(order[idx]);
                idx++;
            }
            // 筛选 r_i >= k 入堆
            filterAndPush(queue, r, real, used, k, pq);

            // 主循环：每次取出最大 real 更新 k
            while (!pq.isEmpty()) {
                long[] top = pq.poll();
                long gain = top[0];
                int i = (int) top[1];
                k = Math.max(k, gain);
                answer = Math.max(answer, k);

                // 扩展：新增所有 l_i <= k
                while (idx < n && l[order[idx]] <= k) {
                    queue.addLast(order[idx]);
                    idx++;
                }
                // 再次筛选新可用的入堆
                filterAndPush(queue, r, real, used, k, pq);
            }
            out.println(answer);
        }
        out.close();
        out.close();
        br.close();
    }

    private static void filterAndPush(
            Deque<Integer> queue,
            int[] r,
            long[] real,
            boolean[] used,
            long k,
            PriorityQueue<long[]> pq) {
        Iterator<Integer> it = queue.iterator();
        while (it.hasNext()) {
            int i = it.next();
            if (r[i] < k) {
                // 不再可以访问，丢弃
                it.remove();
            } else if (!used[i]) {
                used[i] = true;
                // pq 存 [real_i, index]
                pq.add(new long[]{real[i], i});
                it.remove();
            }
        }
    }

}