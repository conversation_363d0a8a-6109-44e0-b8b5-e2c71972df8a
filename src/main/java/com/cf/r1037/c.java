package com.cf.r1037;

import java.io.*;
import java.util.Arrays;
import java.util.TreeSet;

public class c {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken();
        int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken(); int n = (int) in.nval; // 塔数
            in.nextToken(); int k = (int) in.nval - 1; // 初始索引
            int[] h = new int[n];
            for (int i = 0; i < n; i++) {
                in.nextToken();
                h[i] = (int) in.nval;
            }
            int start = h[k];
            int target = start;
            for (int x : h) if (x > target) target = x;
            if (start == target) {
                out.println("YES");
                continue;
            }

            TreeSet<Integer> ts = new TreeSet<>();
            for (int x : h) {
                if (x > start) ts.add(x);
            }

            long T = 0;
            int cur = start;
            boolean ok = false;
            for (int nxt : ts) {
                long d = (long) nxt - cur;
                if (T + d > cur) {
                    break;
                }
                T += d;
                cur = nxt;
                if (cur == target) {
                    ok = true;
                    break;
                }
            }

            out.println(ok ? "YES" : "NO");
        }
        out.close();
        out.close();
        br.close();
    }

}