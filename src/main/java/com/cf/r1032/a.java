package com.cf.r1032;

import java.io.*;
import java.util.Arrays;

public class a {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken();
        int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken(); int n = (int) in.nval;
            in.nextToken(); int s = (int) in.nval;
            int[] arr = new int[n];
            for (int i = 0; i < n; i++) {
                in.nextToken();
                arr[i] = (int) in.nval;
            }
            int l = arr[0];
            int r = arr[n - 1];
            if (s == l && s == r) {
                out.println(0);
                continue;
            } else if (s >= l && s <= r) {
                out.println(Math.min(s - l + r - 1, r - s + r - 1));
            } else if (s > r) {
                out.println(s - l);
            } else {
                out.println(r - s);
            }
        }
        out.close();
        out.close();
        br.close();
    }
}
