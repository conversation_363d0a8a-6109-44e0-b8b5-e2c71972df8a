package com.cf.r1031;

import java.io.*;
import java.util.StringTokenizer;

public class c {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StringTokenizer st = new StringTokenizer(br.readLine());
        PrintWriter out = new PrintWriter(System.out);
        int t = Integer.parseInt(st.nextToken());
        while (t-- > 0) {
            st = new StringTokenizer(br.readLine());
            int n = Integer.parseInt(st.nextToken());
            int m = Integer.parseInt(st.nextToken());
            int k = Integer.parseInt(st.nextToken());
            String[][] arr = new String[n][m];
            for (int i = 0; i < n; i++) {
                st = new StringTokenizer(br.readLine());
                String[] s = st.nextToken().split("");
                System.arraycopy(s, 0, arr[i], 0, m);
            }
            int[][] f = new int[n + 1][m + 1];
            for (int i = 0; i < n; i++) {
                for (int j = 0; j < m; j++) {
                    f[i + 1][j + 1] = arr[i][j].equals("g") ? 1 : 0;
                    f[i + 1][j + 1] += f[i + 1][j] + f[i][j + 1] - f[i][j];
                }
            }
            int ans = n * m;
            for (int i = 0; i < n; i++) {
                for (int j = 0; j < m; j++) {
                    if (arr[i][j].equals(".")) {
                        int x1 = Math.max(0, i - k + 1), x2 = Math.min(n - 1, i + k - 1);
                        int y1 = Math.max(0, j - k + 1), y2 = Math.min(m - 1, j + k - 1);
                        ans = Math.min(ans, f[x2 + 1][y2 + 1] - f[x1][y2 + 1] - f[x2 + 1][y1] + f[x1][y1]);
                    }
                }
            }
            out.println(f[n][m] - ans);
        }
        out.close();
        out.close();
        br.close();
    }

}
