package com.cf.r1031;

import java.io.*;

public class a {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken();
        int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken(); int k = (int) in.nval;
            in.nextToken(); int a = (int) in.nval;
            in.nextToken(); int b = (int) in.nval;
            in.nextToken(); int x = (int) in.nval;
            in.nextToken(); int y = (int) in.nval;
            int ans = 0;
            if (x > y) {
                ans = getAns(k, a, b, x, y, ans);
            } else {
                ans = getAns(k, b, a, y, x, ans);
            }
            out.println(ans);
        }
        out.close();
        out.close();
        br.close();
    }

    private static int getAns(int k, int a, int b, int x, int y, int ans) {
        if (k >= b) {
            int p = (k - b) / y;
            ans += p + 1;
            k -= y * (p + 1);
        }
        if (k >= a) {
            ans += (int) ((k - a) / x) + 1;
        }
        return ans;
    }
}
