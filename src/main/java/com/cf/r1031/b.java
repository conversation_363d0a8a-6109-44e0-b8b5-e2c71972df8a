package com.cf.r1031;

import java.io.*;

public class b {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken();
        int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken(); int w = (int) in.nval;
            in.nextToken(); int h = (int) in.nval;
            in.nextToken(); int a = (int) in.nval;
            in.nextToken(); int b = (int) in.nval;
            in.nextToken(); int x1 = (int) in.nval;
            in.nextToken(); int y1 = (int) in.nval;
            in.nextToken(); int x2 = (int) in.nval;
            in.nextToken(); int y2 = (int) in.nval;
            if (x2 >= x1 + a || x1 >= x2 + a) {
                if (Math.abs(x1 - x2) % a == 0) {
                    out.println("Yes");
                    continue;
                }
            }
            if (y2 >= y1 + b || y1 >= y2 + b) {
                if (Math.abs(y1 - y2) % b == 0) {
                    out.println("Yes");
                    continue;
                }
            }
            out.println("No");
        }
        out.flush();
        out.close();
        br.close();
    }

}
