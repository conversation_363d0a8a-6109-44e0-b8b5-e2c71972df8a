package com.cf.r1031;

import java.io.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


// TODO
public class f {

    static boolean[] vis;
    static int[] fa;
    static boolean[] tree;
    static List<List<int[]>> graph;

    static boolean[] back;
    static int[] a;
    static int[] b;
    static int root;

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken();
        int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken(); int n = (int) in.nval;
            a = new int[n];
            b = new int[n];
            for (int i = 0; i < n; i++) {
                in.nextToken();
                a[i] = (int) in.nval;
            }
            for (int i = 0; i < n; i++) {
                in.nextToken();
                b[i] = (int) in.nval;
            }

            graph = new ArrayList<>();
            for (int i = 0; i < 2 * n + 1; i++) {
                graph.add(new ArrayList<>());
            }

            for (int i = 0; i < n; i++) {
                if (a[i] != b[i]) {
                    graph.get(a[i]).add(new int[] {b[i], i});
                    graph.get(b[i]).add(new int[] {a[i], i});
                }
            }
            int m = n * 2 + 1;
            vis = new boolean[m];
            tree = new boolean[n];
            fa = new int[m];
            Arrays.fill(fa, -1);

            // 第一回合
            for (int i = 0; i < m; i++) {
                dfs(i, -1);
            }

            root = -1;
            back = new boolean[n];

            out.println();
        }
        out.flush();
        out.close();
        br.close();
    }

    public static void dfs(int x, int fath) {
        if (vis[x]) return;
        vis[x] = true;
        for (int[] t : graph.get(x)) {
            int y = t[0];
            if (y == fath) continue;
            if (!vis[y]) {
                tree[t[1]] = true;
                fa[y] = x;
                dfs(y, x);
            }
        }
    }

    public static void dfs2(int x, boolean down) {
        if (vis[x]) return;
        vis[x] = true;
        for (int[] t : graph.get(x)) {
            int y = t[0];
            if (tree[t[1]]) {
                if (y == fa[x]) continue;
                if (x == root) down = !down;
                if (down != (a[t[1]] == x)) {
                    swap(a[t[1]], b[t[1]]);
                }
            }
        }
    }

    public static void swap(int a, int b) {
        int temp = fa[a];
    }

}
