package com.cf.r1034;

import java.io.*;
import java.util.Arrays;

// 第k小的数
public class b {

    public static int k;
    public static int first, last;

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken();
        int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken(); int n = (int) in.nval;
            in.nextToken(); int j = (int) in.nval;
            in.nextToken(); int kth = (int) in.nval;
            int[] arr = new int[n];
            for (int i = 0; i < n; i++) {
                in.nextToken();
                arr[i] = (int) in.nval;
            }
            j--;
            int x = arr[j];
            arr = Arrays.stream(arr).distinct().toArray();
            k = arr.length - kth;
            if (k <= 0) {
                out.println("Yes");
            }
            int i = quickSelect(arr, 0, arr.length - 1);
            if (x >= i) {
                out.println("Yes");
            } else {
                out.println("No");
            }
        }
        out.close();
        out.close();
        br.close();
    }

    private static int quickSelect(int[] arr, int l, int r) {
        if (l == r) return arr[l];
        int x = arr[l + (int) (Math.random() * (r - l + 1))];
        partition(arr, l, r, x);
        if (first <= k && k <= last) {
            return arr[k];
        } else if (k < first) {
            return quickSelect(arr, l, first - 1);
        } else {
            return quickSelect(arr, last + 1, r);
        }
    }

    private static void partition(int[] arr, int l, int r, int x) {
        first = l;
        last = r;
        int i = l;
        while (i <= last) {
            if (arr[i] == x) {
                i++;
            } else if (arr[i] < x) {
                swap(arr, i++, first++);
            } else {
                swap(arr, i, last--);
            }
        }
    }

    private static void swap(int[] arr, int i, int j) {
        int temp = arr[i];
        arr[i] = arr[j];
        arr[j] = temp;
    }
}
