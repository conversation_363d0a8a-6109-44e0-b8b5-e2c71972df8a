package com.cf.r1034;

import java.io.*;

public class a {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken();
        int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken();
            int n = (int) in.nval;
            boolean[] visited = new boolean[n];
            int a = 0;
            int b = 3;
            for (int i = 0; i < n; i++) {
                while (a < n && visited[a]) {
                    a++;
                }
                if (a < n) {
                    visited[a] = true;
                } else {
                    break;
                }
                b = 0;
                while (b < n && ((a + b) % 4 != 3 || visited[b])) {
                    b++;
                }
                if (b < n) {
                    visited[b] = true;
                } else {
                    break;
                }
            }
            if (a >= n) {
                out.println("Bob");
            } else {
                out.println("Alice");
            }

        }
        out.close();
        out.close();
        br.close();
    }

    public static int mod4(int a, int b) {
        return (a + b) % 4;
    }
}