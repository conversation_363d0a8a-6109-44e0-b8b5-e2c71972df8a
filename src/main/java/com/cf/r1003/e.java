package com.cf.r1003;

import java.io.*;
import java.util.Arrays;

public class e {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken(); int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken(); int n = (int) in.nval;
            in.nextToken(); int m = (int) in.nval;
            in.nextToken(); int k = (int) in.nval;
            int x  = Math.max(n, m);
            int y = Math.min(n, m);
            char major = n >= m ? '0' : '1';
            char minor = n >= m ? '1' : '0';

            if (y == 0) {
                if (x == k) {
                    for (int i = 0; i < x; i++) out.print(major);
                    out.println();
                } else {
                    out.println(-1);
                }
                continue;
            }

            if (!((y + 1) * (k - 1) < x && x <= (y + 1) * k)) {
                out.println(-1);
                continue;
            }

            int group = y + 1;
            int smallSize = x / group;
            int rem = x % group;
            int bigSize = smallSize + 1;
        }
        out.close();
        out.close();
        br.close();
    }

}
