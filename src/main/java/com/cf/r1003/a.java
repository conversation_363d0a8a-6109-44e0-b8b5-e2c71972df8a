package com.cf.r1003;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;

public class a {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        PrintWriter out = new PrintWriter(System.out);
        int t = Integer.parseInt(br.readLine());
        while (t-- > 0) {
            String w = br.readLine();
            int n = w.length();
            out.println(w.substring(0, n - 2) + "i");
        }
        out.close();
        out.close();

        br.close();
    }
}
