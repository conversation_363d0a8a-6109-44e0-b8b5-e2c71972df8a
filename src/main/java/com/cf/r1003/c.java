package com.cf.r1003;

import java.io.*;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.TreeSet;

public class c {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken(); int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken(); int n = (int) in.nval;
            in.nextToken(); int m = (int) in.nval;
            int[] a = new int[n];
            for (int i = 0; i < n; i++) {
                in.nextToken(); a[i] = (int) in.nval;
            }
            int[] b = new int[m];
            for (int i = 0; i < m; i++) {
                in.nextToken(); b[i] = ((int) in.nval);
            }
            Arrays.sort(b);
            if (n <= 1) {
                out.println("Yes");
                continue;
            }
            boolean ans = true;
            long pre = Long.MIN_VALUE / 4;
            long maxB = b[m - 1];
            for (int x : a) {
                long best = x >= pre ? x : Long.MAX_VALUE;
                long need = pre + x;
                if (need <= maxB) {
                    int lo = 0, hi = m;
                    while (lo < hi) {
                        int mid = (lo + hi) >>> 1;
                        if ((long) b[mid] < need) lo = mid + 1;
                        else hi = mid;
                    }
                    long v = (long) b[lo] - x;
                    if (v < best) best = v;
                }
                if (best == Long.MAX_VALUE) {
                    ans = false;
                    break;
                }
                pre = best;
            }
            out.println(ans ? "YES" : "NO");
        }
        out.close();
        out.close();
        br.close();
    }

    static int lowerBound(int[] arr, long key) {
        int l = 0, r = arr.length;
        while (l < r) {
            int mid = (l + r) >>> 1;
            if ((long)arr[mid] < key) l = mid + 1;
            else r = mid;
        }
        return l;
    }

}
