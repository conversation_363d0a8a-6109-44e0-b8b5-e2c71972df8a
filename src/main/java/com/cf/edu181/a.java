package com.cf.edu181;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;

public class a {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        PrintWriter out = new PrintWriter(System.out);
        int t = Integer.parseInt(br.readLine());
        while (t-- > 0) {
            String s = br.readLine();
            int n = s.length();
            for (int i = 0; i < n - 2; i++) {
                if ((s.charAt(i) == 'F' && s.charAt(i + 1) == 'F' && s.charAt(i + 2) == 'T')
                        || (s.charAt(i) == 'N' && s.charAt(i + 1) == 'T' && s.charAt(i + 2) == 'T')) {
                    s = swapStr(s);
                    break;
                }
            }
            out.println(s);
        }
        out.close();
        out.close();
        br.close();
    }

    public static String swapStr(String str) {
        char[] s = str.toCharArray();
        int l = 0, r = 0;
        while (r < s.length) {
            if (s[r] == 'T') {
                swap(s, l++, r);
            }
            r++;
        }
        return new String(s);
    }

    public static void swap(char[] s, int l, int r) {
        char temp = s[l];
        s[l] = s[r];
        s[r] = temp;
    }
}
