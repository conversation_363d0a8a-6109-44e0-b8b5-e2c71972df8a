package com.cf.r971;

import java.io.*;

public class c {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken(); int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken();
            long x = (long) in.nval;
            in.nextToken();
            long y = (long) in.nval;
            in.nextToken();
            long k = (long) in.nval;

            long A = (x + k - 1) / k;
            long B = (y + k - 1) / k;

            long m;
            if (A > B) {
                m = 2 * A - 1;
            } else {
                m = 2 * B;
            }

            out.println(m);
        }
        out.flush();
        out.close();
        br.close();
    }
}
