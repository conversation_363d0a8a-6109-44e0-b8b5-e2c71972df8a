package com.cf.r971;

import java.io.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class b {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        PrintWriter out = new PrintWriter(System.out);
        int t = Integer.parseInt(br.readLine());
        while (t-- > 0) {
            int n = Integer.parseInt(br.readLine());
            List<Integer> ans = new ArrayList<>();
            while (n-- > 0) {
                String s = br.readLine();
                ans.add(s.indexOf("#") + 1);
            }
            Collections.reverse(ans);
            for (Integer an : ans) {
                out.print(an + " ");
            }
            out.println();
        }
        out.close();
        out.close();
        br.close();
    }
}
