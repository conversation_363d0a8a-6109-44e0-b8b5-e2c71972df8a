package com.cf.r971;

import java.io.*;
import java.util.StringTokenizer;

public class g {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken(); int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken();
            int n = (int) in.nval;
            in.nextToken();
            int k = (int) in.nval;
            in.nextToken();
            int q = (int) in.nval;

            int[] a = new int[n];
            for (int i = 0; i < n; i++) {
                in.nextToken(); a[i] = (int) in.nval;
            }

            int[] brk = new int[n];
            brk[0] = 0;
            for (int i = 1; i < n; i++) {
                brk[i] = (a[i] == a[i - 1] + 1) ? 0 : 1;
            }

            int[] pre = new int[n];
            pre[0] = brk[0];
            for (int i = 1; i < n; i++) {
                pre[i] = pre[i - 1] + brk[i];
            }

            while (q-- > 0) {
                in.nextToken(); int l = (int) in.nval - 1;
                in.nextToken(); int r = (int) in.nval - 1;
                out.println(pre[r] - pre[l]);
            }
        }
        out.flush();
        out.close();
        br.close();
    }
}
