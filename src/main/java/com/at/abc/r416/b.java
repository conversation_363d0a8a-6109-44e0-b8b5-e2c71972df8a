package com.at.abc.r416;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;

public class b {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        PrintWriter out = new PrintWriter(System.out);
        String s = br.readLine().trim();
        boolean pre = true;
        StringBuilder ans = new StringBuilder();
        for (int i = 0; i < s.length(); i++) {
            if (s.charAt(i) == '#') {
                ans.append('#');
                pre = true;
            } else if (pre) {
                ans.append("o");
                pre = false;
            } else {
                ans.append(".");
            }
        }
        out.println(ans);
        out.flush();
        out.close();
        br.close();
    }
}
