package com.at.abc.r416;

import java.io.*;

public class a {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        PrintWriter out = new PrintWriter(System.out);
        String[] str = br.readLine().split(" ");
        int n = Integer.parseInt(str[0]);
        int l = Integer.parseInt(str[1]) - 1;
        int r = Integer.parseInt(str[2]) - 1;
        String s = br.readLine();
        boolean ans = true;
        for (int i = l; i <= r; i++) {
            if (s.charAt(i) != 'o') {
                ans = false;
            }
        }
        out.println(ans ? "Yes" : "No");
        out.flush();
        out.close();
        br.close();
    }
}
