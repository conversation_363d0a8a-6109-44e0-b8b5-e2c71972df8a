package com.at.abc.r416;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;

public class c {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        PrintWriter out = new PrintWriter(System.out);
        String[] str = br.readLine().trim().split(" ");
        int n = Integer.parseInt(str[0]);
        int k = Integer.parseInt(str[1]);
        int x = Integer.parseInt(str[2]);
        String[] s = new String[n];
        for (int i = 0; i < n; i++) {
            s[i] = br.readLine().trim();
        }
        ArrayList<String> list = new ArrayList<>();
        if (k == 1) {
            list = new ArrayList<>(Arrays.asList(s));
        } else if (k == 2) {
            for (int i = 0; i < n; i++) {
                for (int j = 0; j < n; j++) {
                    list.add(s[i] + s[j]);
                }
            }
        } else if (k == 3) {
            for (int i = 0; i < n; i++) {
                for (int j = 0; j < n; j++) {
                    for (int l = 0; l < n; l++) {
                        list.add(s[i] + s[j] + s[l]);
                    }
                }
            }
        } else if (k == 4) {
            for (int i = 0; i < n; i++) {
                for (int j = 0; j < n; j++) {
                    for (int l = 0; l < n; l++) {
                        for (int m = 0; m < n; m++) {
                            list.add(s[i] + s[j] + s[l] + s[m]);
                        }
                    }
                }
            }
        } else if (k == 5) {
            for (int i = 0; i < n; i++) {
                for (int j = 0; j < n; j++) {
                    for (int l = 0; l < n; l++) {
                        for (int m = 0; m < n; m++) {
                            for (int o = 0; o < n; o++) {
                                list.add(s[i] + s[j] + s[l] + s[m] + s[o]);
                            }
                        }
                    }
                }
            }
        }
        Collections.sort(list, Comparator.naturalOrder());
        out.println(list.get(x - 1));
        out.flush();
        out.close();
        br.close();
    }
}
