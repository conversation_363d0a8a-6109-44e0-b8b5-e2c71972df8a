package com.at.abc.r416;

import java.io.*;
import java.util.Arrays;
import java.util.Comparator;

public class d {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken(); int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken(); int n = (int) in.nval;
            in.nextToken(); int m = (int) in.nval;
            int[] a = new int[n];
            for (int i = 0; i < n; i++) {
                in.nextToken(); a[i] = (int) in.nval;
                a[i] %= m;
            }
            int[] b = new int[n];
            for (int i = 0; i < n; i++) {
                in.nextToken(); b[i] = ((int) in.nval);
                b[i] %= m;
            }

            a = Arrays.stream(a).boxed().sorted(Comparator.reverseOrder()).mapToInt(Integer::intValue).toArray();
            Arrays.sort(b);

            int p = 0;
            for (int i = 0; i < n; i++) {
                while (p < n && a[i] + b[p] < m) p++;
                if (p >= n) break;
                p++;
                a[i] -= m;
            }
            int answer = 0;
            for (int i = 0; i < n; i++) {
                answer += a[i] + b[i];
            }
            out.println(answer);
        }
        out.close();
        out.close();
        br.close();
    }

}
