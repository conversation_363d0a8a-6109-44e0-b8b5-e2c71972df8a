package com.at.abc.r416;

import java.io.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

// Djikstra
public class e {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken(); int T = (int) in.nval;
        while (T-- > 0) {
            in.nextToken(); int n = (int) in.nval;
            in.nextToken(); int m = (int) in.nval;
            List<List<int[]>> graph = new ArrayList<>();
            for (int i = 0; i < n; i++) {
                graph.add(new ArrayList<>());
            }
            for (int i = 0; i < m; i++) {
                in.nextToken(); int u = (int) in.nval;
                in.nextToken(); int v = (int) in.nval;
                in.nextToken(); int w = (int) in.nval;
                graph.get(u - 1).add(new int[]{v - 1, w});
                graph.get(v - 1).add(new int[]{u - 1, w});
            }
            in.nextToken(); int k = (int) in.nval;
            in.nextToken(); int t = (int) in.nval;
            boolean[] plant = new boolean[n];
            for (int i = 0; i < k; i++) {
                in.nextToken(); int p = (int) in.nval;
                plant[p - 1] = true;
            }
            in.nextToken(); int q = (int) in.nval;
            while (q-- > 0) {
                in.nextToken(); int e = (int) in.nval;
                if (e == 1) {
                    in.nextToken(); int x = (int) in.nval;
                    in.nextToken(); int y = (int) in.nval;
                    in.nextToken(); int tt = (int) in.nval;
                    graph.get(x - 1).add(new int[]{y - 1, tt});
                    graph.get(y - 1).add(new int[]{x - 1, tt});
                } else if (e == 2) {
                    in.nextToken(); int x = (int) in.nval;
                    plant[x - 1] = true;
                } else if (e == 3) {
                    int sum = 0;
                }
            }
        }
        out.close();
        out.close();
        br.close();
    }

}
