package com.at.abc.r411;

import java.io.*;

public class b {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken();
        int n = (int) in.nval;
        int[] arr = new int[n];
        for (int i = 1; i < n; i++) {
            in.nextToken();
            arr[i] = (int) in.nval;
        }
        int[] prefix = new int[n];
        for (int i = 1, sum = 0; i < n; i++) {
            sum += arr[i];
            prefix[i] = sum;
        }
        for (int i = 1; i < n; i++) {
            for (int j = i; j < n; j++) {
                out.print(prefix[j] - prefix[i - 1] + " ");
            }
            out.println();
        }
        out.flush();
        out.close();
        br.close();
    }
}
