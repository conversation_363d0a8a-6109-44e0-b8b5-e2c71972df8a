package com.at.abc.r411;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.util.Arrays;

public class d {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        PrintWriter out = new PrintWriter(System.out);
        String[] nq = br.readLine().split(" ");
        int n = Integer.parseInt(nq[0]);
        int q = Integer.parseInt(nq[1]);
        String s = "";
        String[] p = new String[n + 1];
        Arrays.fill(p, "");
        for (int i = 0; i < q; i++) {
            String[] query = br.readLine().split(" ");
            if (query[0].equals("1")) {
                int x = Integer.parseInt(query[1]);
                p[x] = s;
            } else if (query[0].equals("2")) {
                String ss = query[2];
                int x = Integer.parseInt(query[1]);
                p[x] = p[x] + ss;
            } else if (query[0].equals("3")) {
                int x = Integer.parseInt(query[1]);
                s = p[x];
            }
        }
        out.println(s);
        out.flush();
        out.close();
        br.close();
    }
}
