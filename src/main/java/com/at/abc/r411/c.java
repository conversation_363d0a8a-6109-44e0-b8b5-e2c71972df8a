package com.at.abc.r411;

import java.io.*;
import java.util.Arrays;

public class c {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);

        in.nextToken();
        int n = (int) in.nval;
        int[] arrstart = new int[n + 2];
        Arrays.fill(arrstart, 1);

        in.nextToken();
        int q = (int) in.nval;
        int[] arr = new int[q];
        for (int i = 0; i < q; i++) {
            in.nextToken();
            arr[i] = (int) in.nval;
        }

        int segments = 0;

        for (int i = 0; i < q; i++) {
            int x = arr[i];

            if (arrstart[x] == -1) {
                if (arrstart[x - 1] == -1 && arrstart[x + 1] == -1) {
                    segments++;
                }
                if (arrstart[x - 1] != -1 && arrstart[x + 1] != -1) {
                    segments--;
                }
            }
            arrstart[x] *= -1;

            if (arrstart[x] == -1) {
                if (arrstart[x - 1] == -1 && arrstart[x + 1] == -1) {
                    segments--;
                }
                if (arrstart[x - 1] != -1 && arrstart[x + 1] != -1) {
                    segments++;
                }
            }
            out.println(segments);
        }
        out.flush();
        out.close();
        br.close();
    }

    public static int compute(int[] arr) {
        int ans = 0;
        int i = 1;
        while (i < arr.length) {
            if (arr[i] == -1) {
                ans++;
                while (i < arr.length && arr[i] == -1) {
                    i++;
                }
            }
            i++;
        }
        return ans;
    }
}
