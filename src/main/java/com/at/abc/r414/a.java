package com.at.abc.r414;

import java.io.*;
import java.util.HashSet;
import java.util.Set;

public class a {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken(); int n = (int) in.nval;
        in.nextToken(); int l = (int) in.nval;
        in.nextToken(); int r = (int) in.nval;
        int ans = 0;
        for (int i = 0; i < n; i++) {
            in.nextToken(); int x = (int) in.nval;
            in.nextToken(); int y = (int) in.nval;
            if (x <= l && y >= r) {
                ans++;
            }
        }
        out.println(ans);
        out.flush();
        out.close();
        br.close();
    }
}
