package com.at.abc.r414;

import java.io.*;

public class b {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        PrintWriter out = new PrintWriter(System.out);
        int n = Integer.parseInt(br.readLine());
        StringBuilder ans = new StringBuilder();
        long count = 0;
        for (int i = 0; i < n; i++) {
            String s = br.readLine();
            String c = s.split(" ")[0];
            long l = Long.parseLong(s.split(" ")[1]);
            if (l > 100) {
                count += 101;
                break;
            }
            for (int j = 0; j < l; j++) {
                ans.append(c);
            }
            count += l;
            if (count > 100) {
                break;
            }
        }
        if (count > 100) {
            out.println("Too Long");
        } else {
            out.println(ans.toString());
        }

        out.flush();
        out.close();
        br.close();
    }
}
