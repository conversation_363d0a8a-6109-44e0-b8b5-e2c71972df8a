package com.at.abc.r414;

import java.io.*;

public class c {

    public static void main(String[] args) throws Exception {
        BufferedReader in = new BufferedReader(new InputStreamReader(System.in));
        int A = Integer.parseInt(in.readLine().trim());
        long N = Long.parseLong(in.readLine().trim());
        in.close();

        long ans = 0;

        // 枚举十进制回文数的长度 L = 1..12
        for (int L = 1; L <= 12; L++) {
            int half = (L + 1) / 2;  // 构造前半部分的长度
            long start = (long) Math.pow(10, half - 1);
            long end   = (long) Math.pow(10, half) - 1;
            if (half == 1) start = 0; // L=1 时允许前半部分从 0 开始

            for (long prefix = start; prefix <= end; prefix++) {
                String s = Long.toString(prefix);
                // 如果 L 是奇数，mirror 时去掉最后一位
                String rev = new StringBuilder(
                                 s.substring(0, L % 2 == 0 ? s.length() : s.length() - 1)
                              ).reverse().toString();
                long pal = Long.parseLong(s + rev);
                if (pal == 0 || pal > N) continue;
                if (isPalindromeInBase(pal, A)) {
                    ans += pal;
                }
            }
        }

        System.out.println(ans);
    }

    // 判断 x 的 base- A 表示是否为回文
    private static boolean isPalindromeInBase(long x, int base) {
        char[] buf = new char[64];
        int len = 0;
        while (x > 0) {
            buf[len++] = (char)('0' + (x % base));
            x /= base;
        }
        for (int i = 0; i < len / 2; i++) {
            if (buf[i] != buf[len - 1 - i]) return false;
        }
        return true;
    }
}
