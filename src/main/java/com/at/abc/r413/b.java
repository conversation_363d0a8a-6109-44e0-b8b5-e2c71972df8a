package com.at.abc.r413;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.util.HashSet;
import java.util.Set;

public class b {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        PrintWriter out = new PrintWriter(System.out);
        int n = Integer.parseInt(br.readLine());
        Set<String> set = new HashSet<>();
        int ans = 0;
        String[] arr = new String[n];
        for (int i = 0; i < n; i++) {
            String s = br.readLine();
            arr[i] = s;
        }
        for (int i = 0; i < n; i++) {
            for (int j = i + 1; j < n; j++) {
                String s = arr[i] + arr[j];
                if (!set.contains(s)) {
                    ans++;
                    set.add(s);
                }
                s = arr[j] + arr[i];
                if (!set.contains(s)) {
                    ans++;
                    set.add(s);
                }
            }
        }
        out.println(ans);
        out.flush();
        out.close();
        br.close();
    }
}
