package com.at.abc.r413;

import java.io.*;
import java.util.*;

public class c {

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken();
        int q = (int) in.nval;
        LinkedList<int[]> list = new LinkedList<>();
        while (q-- > 0) {
            in.nextToken();
            int w = (int) in.nval;
            if (w == 1) {
                in.nextToken(); int c = (int) in.nval;
                in.nextToken(); int x = (int) in.nval;
                list.addLast(new int[] { x, c });
            } else if (w == 2) {
                in.nextToken(); int k = (int) in.nval;
                long sum = 0;
                while (k > 0 && !list.isEmpty()) {
                    int[] first = list.getFirst();
                    int available = first[1];
                    // 真正要消耗的数量
                    int cnt = Math.min(k, available);
                    sum += (long) cnt * first[0];
                    k -= cnt;
                    first[1] -= cnt;
                    if (first[1] == 0) {
                        list.removeFirst();
                    }
                }
                out.println(sum);
            }
        }
        out.flush();
        out.close();
        br.close();
    }
}
