package com.at.abc.r413;

import java.io.*;
import java.util.Arrays;
import java.util.Comparator;
import java.util.LinkedList;

public class d {

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken();
        int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken();
            int n = (int) in.nval;
            Integer[] arr = new Integer[n];
            boolean allPositive = true;
            boolean allNegative = true;
            for (int i = 0; i < n; i++) {
                in.nextToken();
                arr[i] = (int) in.nval;
                if (arr[i] > 0) allNegative = false;
                if (arr[i] < 0) allPositive = false;
            }
            boolean flag;
            if (allPositive || allNegative) {
                Arrays.sort(arr);
                flag = check(n, arr);
            } else {
                Arrays.sort(arr, Comparator.comparingInt(Math::abs));
                boolean f1 = check(n, arr);
                Arrays.sort(arr, (a, b) -> Math.abs(b) - Math.abs(a));
                boolean f2 = check(n, arr);
                flag = f1 || f2;
            }
            out.println(flag ? "Yes" : "No");
        }
        out.flush();
        out.close();
        br.close();
    }

    // 正确的比例检查：a[i] * a[i-1] == a[i-1] * a[i-2] * 公比²，直接用 cross multiply 避免浮点误差
    private static boolean check(int n, Integer[] arr) {
        if (n <= 2) return true;
        for (int i = 2; i < n; i++) {
            long lhs = (long) arr[i] * arr[i - 2];  // a[i] * a[i-2]
            long rhs = (long) arr[i - 1] * arr[i - 1]; // a[i-1]^2
            if (lhs != rhs) {
                return false;
            }
        }
        return true;
    }
}
