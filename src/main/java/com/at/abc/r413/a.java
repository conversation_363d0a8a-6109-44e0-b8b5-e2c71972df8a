package com.at.abc.r413;

import java.io.*;

public class a {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(System.out);
        in.nextToken(); int n = (int) in.nval;
        in.nextToken(); int m = (int) in.nval;
        int sum = 0;
        for (int i = 0; i < n; i++) {
            in.nextToken();
            sum += (int) in.nval;
        }
        if (sum <= m) {
            out.println("Yes");
        } else {
            out.println("No");
        }
        out.flush();
        out.close();
        br.close();
    }
}
