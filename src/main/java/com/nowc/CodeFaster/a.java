package com.nowc.CodeFaster;

import java.io.*;
import java.util.Comparator;
import java.util.PriorityQueue;

public class a {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));
        in.nextToken(); int n = (int) in.nval;
        in.nextToken(); long k = (long) in.nval;
        PriorityQueue<Long> pq = new PriorityQueue<>();
        for (int i = 0; i < n; i++) {
            in.nextToken(); pq.add((long) in.nval);
        }
        for (int i = 0; i < n - 1; i++) {
            pq = compute(pq, k);
        }
        out.println(pq.peek());
        out.flush();
    }

    public static PriorityQueue<Long> compute(PriorityQueue<Long> pq, long k) {
        PriorityQueue<Long> tmp = new PriorityQueue<>();
        int n = pq.size();
        for (int i = 0; i < n; i++) {
            Long poll = pq.poll();
            poll ^= k;
            tmp.add(poll);
        }
        tmp.poll();
        return tmp;

    }
}
