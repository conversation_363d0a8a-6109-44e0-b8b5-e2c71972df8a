package com.nowc.week.w99;

import java.io.*;
import java.util.*;

public class e {

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));
        in.nextToken(); int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken();
            int n = (int) in.nval;
            int[] arr = new int[n + 2];
            Arrays.fill(arr, Integer.MAX_VALUE);
            Map<Integer, Integer> mp = new HashMap<>();
            // 统计频次
            Map<Integer, Integer> freq = new HashMap<>();
            for (int i = 1; i <= n; i++) {
                in.nextToken();
                arr[i] = (int) in.nval;
                mp.put(arr[i], i);
                freq.put(arr[i], freq.getOrDefault(arr[i], 0) + 1);
            }

            int r = n;
            while (r >= 1 && arr[r] < arr[r + 1] && freq.get(arr[r]) == 1 && arr[r] >= r) {
                r--;
            }
            if (r == 0) {
                out.println(0);
                continue;
            }
            Set<Integer> set = new HashSet<>();
            for (int i = 1; i <= r; i++) {
                set.add(arr[i]);
            }
            int ans = set.size();
            for (int a : set) {
                if (freq.get(a) == 1 && mp.get(a) == a) {
                    ans--;
                }
            }
            out.println(ans);

        }
        out.flush();
        out.close();
        br.close();
    }
}