package com.nowc.week.w99;

import java.io.*;
import java.util.StringTokenizer;

public class d {
    public static void main(String[] args) throws IOException {
        BufferedReader  br  = new BufferedReader(new InputStreamReader(System.in));
        PrintWriter     out = new PrintWriter(new OutputStreamWriter(System.out));
        int t = Integer.parseInt(br.readLine());
        while (t-- > 0) {
            StringTokenizer st = new StringTokenizer(br.readLine());
            long x = Long.parseLong(st.nextToken());
            long p = Long.parseLong(st.nextToken());

            if (p % x == 0) {
                // 第 p/x 个奇数
                long k = p / x;
                out.println(2 * k - 1);
            } else {
                // 第 (p - floor(p/x)) 个偶数
                long cntOdd = p / x;
                long idxEven = p - cntOdd;
                out.println(2 * idxEven);
            }
        }
        out.flush();
    }
}
