package com.nowc.week.w99;

import java.io.*;

public class a {

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));
        String s = br.readLine();
        int n = s.length();
        String ans = "NO";
        for (int i = 0; i < n - 1; i++) {
            if (s.charAt(i) == '9' && s.charAt(i + 1) == '9') {
                ans = "YES";
            }
        }
        out.println(ans);
        out.flush();
        out.close();
        br.close();
    }
}