package com.nowc.week.w99;

import java.io.*;

public class c {

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));
        in.nextToken(); int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken();
            int k = (int) in.nval;
            if (k == 0) {
                out.println("1");
                continue;
            } else if (k == 1) {
                out.println("4");
                continue;
            } else if (k == 2) {
                out.println("8");
                continue;
            }
            String ans = compute(k);
            out.println(ans);
        }
        out.flush();
        out.close();
        br.close();
    }

    public static String compute(int k) {
        StringBuilder s = new StringBuilder();
        if (k % 2 == 0) {
            k /= 2;
            while (k-- > 0) {
                s.append("8");
            }
        } else {
            k /= 2;
            s.append("4");
            while (k-- > 0) {
                s.append("8");
            }
        }
        return s.toString();
    }
}