package com.nowc.week.w99;

import java.io.*;

public class b {

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));
        int t = Integer.parseInt(br.readLine());
        while (t-- > 0) {
            int n = Integer.parseInt(br.readLine());
            String s = br.readLine();
            int ans = 0;
            for (int i = 0; i < n; i++) {
                if (s.charAt(i) > ans) {
                    ans = s.charAt(i);
                }
            }
            out.println(ans);
        }
        out.flush();
        out.close();
        br.close();
    }
}