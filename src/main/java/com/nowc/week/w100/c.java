package com.nowc.week.w100;

import java.io.*;

public class c {

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));
        in.nextToken(); int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken(); int n = (int) in.nval;
            int[] arr = new int[n * 2];
            for (int i = 0; i < n * 2; i++) {
                in.nextToken(); arr[i] = (int) in.nval;
            }
            String ans = "Yes";
            int l = 0, r = 2 * n - 1;
            while (l < r) {
                if (arr[l] != arr[r]) {
                    int x = findNextIndex(arr, l);
                    l = x + 1;
                    if (x == -1) {
                        ans = "No";
                        break;
                    }
                } else {
                    break;
                }
            }
            out.println(ans);
        }
        out.flush();
        out.close();
        br.close();
    }

    private static int findNextIndex(int[] arr, int l) {
        for (int i = l + 1; i < arr.length; i++) {
            if (arr[i] == arr[l]) {
                return i;
            }
        }
        return -1;
    }
}