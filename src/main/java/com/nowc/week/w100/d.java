package com.nowc.week.w100;

import java.io.*;
import java.util.*;

// 线段树
public class d {
    static final int INF = Integer.MAX_VALUE / 2;
    public static void main(String[] args) throws IOException {
        StreamTokenizer in = new StreamTokenizer(new BufferedReader(new InputStreamReader(System.in)));
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));

        in.nextToken(); int n = (int)in.nval;
        int N2 = 2 * n;
        int[] a = new int[N2];
        for(int i = 0; i < N2; i++){
            in.nextToken();
            a[i] = (int)in.nval;
        }

        // 1) 扫一遍拿到每个 i 的 L[i], R[i]
        int[] L = new int[n+1], R = new int[n+1];
        boolean[] seen = new boolean[n+1];
        for(int i = 0; i < N2; i++){
            int x = a[i];
            if(!seen[x]){
                seen[x] = true;
                L[x] = i;
            } else {
                R[x] = i;
            }
        }

        // 2) 计算不交换时的初始权值 S0
        long S0 = 0;
        for(int i = 1; i <= n; i++){
            S0 += (R[i] - L[i] - 1);
        }

        // 3) 按 R[i] 排序，准备「分堆」扫描
        class Pair { int r, l; }
        Pair[] ps = new Pair[n];
        for(int i = 1; i <= n; i++){
            ps[i-1] = new Pair();
            ps[i-1].r = R[i];
            ps[i-1].l = L[i];
        }
        Arrays.sort(ps, Comparator.comparingInt(p -> p.r));

        // 4) 建 4 棵线段树
        //    S1_max : 存 2*Ly - 2*Ry，支持区间 max
        //    S1_min : 存 Ry，支持区间 min
        //    S2_max : 存 Ly，支持区间 max
        //    S2_min : 存 Ly，支持区间 min
        int size = 1;
        while(size < N2) size <<= 1;
        SegmentTreeMax S1_max = new SegmentTreeMax(size, -INF);
        SegmentTreeMin S1_min = new SegmentTreeMin(size, INF);
        SegmentTreeMax S2_max = new SegmentTreeMax(size, -INF);
        SegmentTreeMin S2_min = new SegmentTreeMin(size, INF);

        // S2 初始放入所有 (Ly)
        for(int i = 0; i < n; i++){
            int ly = ps[i].l;
            S2_max.update(ly, ly);
            S2_min.update(ly, ly);
        }

        long bestDelta = 0;

        // 5) 扫描每个 x（按 R 升序），先把它从 S2 移到 S1，再查询增益
        for(int i = 0; i < n; i++){
            int rx = ps[i].r;
            int lx = ps[i].l;

            // 从 S2 中移除这个 y
            S2_max.update(lx, -INF);
            S2_min.update(lx, INF);
            // 加入 S1
            S1_max.update(lx, 2*lx - 2*rx);
            S1_min.update(lx, rx);

            long cur = 0;
            // —— S1 中 y 的两种情况 —— 
            // C: Ry < Rx 且 Ly >= Lx  → Δ = 2*Ly - 2*Ry
            if(lx < N2){
                int vC = S1_max.query(lx, N2-1);
                if(vC > -INF) cur = Math.max(cur, vC);
            }
            // D: Ry < Rx 且 Ly < Lx   → Δ = 2*Lx - 2*Ry
            if(lx > 0){
                int minRy = S1_min.query(0, lx-1);
                if(minRy < INF){
                    cur = Math.max(cur, 2L*lx - 2L*minRy);
                }
            }

            // —— S2 中 y 的两种情况 —— 
            // A: Ry ≥ Rx 且 Ly >= Lx  → Δ = 2*Ly - 2*Rx
            if(lx < N2){
                int maxLy = S2_max.query(lx, N2-1);
                if(maxLy > -INF){
                    cur = Math.max(cur, 2L*maxLy - 2L*rx);
                }
            }
            // B: Ry ≥ Rx 且 Ly < Lx   → Δ = 2*Lx - 2*Rx  （只要存在 Ly < Lx）
            if(lx > 0){
                int minLy = S2_min.query(0, lx-1);
                if(minLy < lx){
                    cur = Math.max(cur, 2L*lx - 2L*rx);
                }
            }

            bestDelta = Math.max(bestDelta, cur);
        }

        out.println(S0 + bestDelta);
        out.flush();
    }

    // 区间最大线段树
    static class SegmentTreeMax {
        int n; int[] st;
        SegmentTreeMax(int N, int init) {
            n = N; st = new int[2*N];
            Arrays.fill(st, init);
        }
        void update(int pos, int val) {
            int i = pos + n; st[i] = val;
            for(i>>=1; i>0; i>>=1) st[i] = Math.max(st[2*i], st[2*i+1]);
        }
        // query [L..R]
        int query(int L, int R) {
            int res = Integer.MIN_VALUE;
            for(int l = L + n, r = R + n; l <= r; l>>=1, r>>=1) {
                if((l&1)==1) res = Math.max(res, st[l++]);
                if((r&1)==0) res = Math.max(res, st[r--]);
            }
            return res;
        }
    }

    // 区间最小线段树
    static class SegmentTreeMin {
        int n; int[] st;
        SegmentTreeMin(int N, int init) {
            n = N; st = new int[2*N];
            Arrays.fill(st, init);
        }
        void update(int pos, int val) {
            int i = pos + n; st[i] = val;
            for(i>>=1; i>0; i>>=1) st[i] = Math.min(st[2*i], st[2*i+1]);
        }
        int query(int L, int R) {
            int res = Integer.MAX_VALUE;
            for(int l = L + n, r = R + n; l <= r; l>>=1, r>>=1) {
                if((l&1)==1) res = Math.min(res, st[l++]);
                if((r&1)==0) res = Math.min(res, st[r--]);
            }
            return res;
        }
    }
}
