package com.nowc.week.w100;

import java.io.*;

public class a {

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));
        in.nextToken();
        int n = (int) in.nval;
        int[] arr = new int[n * 2 ];
        for (int i = 0, j = 1; i < n * 2; i += 2, j++) {
            arr[i] = j;
            arr[i + 1] = j;
        }
        for (int a : arr) {
            out.print(a + " ");
        }
        out.flush();
        out.close();
        br.close();
    }
}