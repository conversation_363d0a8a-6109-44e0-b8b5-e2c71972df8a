package com.nowc.week.w100;

import java.io.*;

public class b {

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));
        in.nextToken();
        int n = (int) in.nval;
        int[] arr = new int[n * 2];
        int l = 0;
        for (l = 0; l < n * 2; l++) {
            in.nextToken();
            arr[l] = (int) in.nval;
            out.print(arr[l] + " ");
            if (l == n - 1) {
                out.println();
            }
        }
        out.flush();
        out.close();
        br.close();
    }
}