package com.nowc.week.w98;

import java.io.*;

public class e {

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));
        in.nextToken();
        int n = (int) in.nval;
        int max = 1000000;
        int[] cnt = new int[max + 1];
        for (int i = 0; i < n; i++) {
            in.nextToken();
            int x = (int) in.nval;
            cnt[x]++;
        }
        long res = 0;
        for (int g = 1; g <= max; g++) {
            long sum = 0;
            for (int k = g; k <= max; k += g) {
                sum += (long) k * cnt[k];
            }
            res = Math.max(res, sum * g);
        }
        out.println(res);
        out.flush();
        out.close();
        br.close();
    }
}