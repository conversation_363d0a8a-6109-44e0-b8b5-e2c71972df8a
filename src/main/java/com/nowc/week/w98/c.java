package com.nowc.week.w98;

import java.io.*;

public class c {

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));
        int n = Integer.parseInt(br.readLine());
        String s = br.readLine();
        char pre = ' ';
        char[] str = s.toCharArray();
        for (int i = 0; i < n - 1; i++) {
            if (str[i] == str[i + 1]) {
                char want = 'r';
                if (s.charAt(i) == 'r' && pre != 'e') {
                    want = 'e';
                }
                if (s.charAt(i) == 'r' && pre != 'd') {
                    want = 'd';
                }
                if (pre == 'r' && s.charAt(i) != 'e') {
                    want = 'e';
                }
                if (pre == 'r' && s.charAt(i) != 'd') {
                    want = 'd';
                }
                str[i] = want;
            }
            pre = str[i];
        }
        out.println(new String(str));
        out.flush();
        out.close();
        br.close();
    }

    public static int gcd(int a, int b) {
        return b == 0 ? a : gcd(b, a % b);
    }
}