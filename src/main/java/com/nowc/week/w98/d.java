package com.nowc.week.w98;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

public class d {

    public static List<List<Integer>> ans = new ArrayList<>();
    public static int n;

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));
        in.nextToken();
        n = (int) in.nval;
        ans.clear();
        dfs(new ArrayList<>(), 0);
        for (List<Integer> an : ans) {
            for (int i = 0; i < an.size(); i++) {
                out.print(an.get(i));
                if (i != an.size() - 1) out.print(" ");
            }
            out.println();
        }
        out.flush();
        out.close();
        br.close();
    }

    public static void dfs(List<Integer> path, int sum) {
        if (sum == n) {
            ans.add(new ArrayList<>(path));
            return;
        }
        for (int i = 1; i <= n - sum; i++) {
            if (!path.isEmpty() && path.get(path.size() - 1) == i) continue;
            path.add(i);
            dfs(path, sum + i);
            path.remove(path.size() - 1);
        }
    }
}