package com.nowc.week.w98;

import java.io.*;
import java.util.Arrays;

public class a {

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));
        while (in.nextToken() != StreamTokenizer.TT_EOF) {
            int x = (int) in.nval;
            if ((x & 1) == 0) {
                out.println("Yes");
            } else {
                out.println("No");
            }

        }
        out.flush();
        out.close();
        br.close();
    }
}