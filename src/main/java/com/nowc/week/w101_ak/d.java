package com.nowc.week.w101_ak;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

public class d {

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));
        in.nextToken();
        int n = (int) in.nval;
        in.nextToken();
        int m = (int) in.nval;

        if ((m & 1) == 0 || Long.highestOneBit(m) > n) {
            out.println(-1);
            out.flush();
            return;
        }

        List<Integer> special = new ArrayList<Integer>();
        for (int i = 0; i < 31; i++) {
            if (((m >> i) & 1) == 1) {
                special.add(1 << i);
            }
        }
        int t = special.size();

        boolean[] used = new boolean[n + 1];
        for (int x : special) {
            used[x] = true;
        }

        List<Integer> tail = new ArrayList<>();
        for (int i = 1; i <= n; i++) {
            if (!used[i]) tail.add(i);
        }
        List<Integer> ans = new ArrayList<>();
        ans.add(1);
        if (tail.size() == 1) {
            ans.add(tail.get(0));
        }
        for (int x : special) {
            if (x != 1) ans.add(x);
        }

        if (tail.size() > 1) {
            ans.addAll(tail);
        }

        boolean[] inPerm = new boolean[n + 1];
        for (int x : ans) inPerm[x] = true;
        for (int x = 1; x <= n; x++) {
            if (!inPerm[x]) ans.add(x);
        }

        List<int[]> intervals = new ArrayList<>();
        if (tail.size() == 1) {
            intervals.add(new int[]{1, 2});
            for (int i = 3, idx = 1; idx < t; idx++, i++) {
                intervals.add(new int[]{i, i});
            }
        } else {
            for (int i = 1; i <= t; i++) {
                intervals.add(new int[]{i, i});
            }
            if (!tail.isEmpty()) {
                intervals.add(new int[]{t + 1, t + tail.size()});
            }
        }
        for (int x : ans) out.print(x + " ");
        out.println();

        out.println(intervals.size());
        for (int[] seg : intervals) {
            out.println(seg[0] + " " + seg[1]);
        }
        out.flush();
    }
}