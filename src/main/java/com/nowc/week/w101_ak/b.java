package com.nowc.week.w101_ak;

import java.io.*;

public class b {

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));
        in.nextToken();
        int n = (int) in.nval;
        out.println(n / 5 * 2 + " " + (n + 5) / 10 + " " + n / 20 * 3 + " " + (n - (n / 20)) * 2);

        out.flush();
        out.close();
        br.close();
    }
}