package com.nowc.week.w101_ak;

import java.io.*;

public class c {

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));
        in.nextToken();
        long n = (long) in.nval;
        long highest = Long.highestOneBit(n);
        long ans = (highest << 1) - 1;
        out.println(ans);

        out.flush();
        out.close();
        br.close();
    }
}