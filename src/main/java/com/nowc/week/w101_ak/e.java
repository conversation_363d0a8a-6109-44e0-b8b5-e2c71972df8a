package com.nowc.week.w101_ak;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.IOException;
import java.io.StreamTokenizer;

public class e {
    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        in.nextToken(); int n = (int) in.nval;
        double[] x = new double[n], y = new double[n];
        for (int i = 0; i < n; i++) {
            in.nextToken(); x[i] = in.nval;
            in.nextToken(); y[i] = in.nval;
        }
        final double ln2 = Math.log(2.0);
        final double threshold = 1.0 / ln2;

        double total = 0.0;
        for (int i = 1; i < n; i++) {
            double dx = x[i] - x[i - 1];
            double dy = y[i] - y[i - 1];
            double d = Math.hypot(dx, dy);

            if (d < threshold) {
                total += 2.0 * d;
            } else {
                double k = Math.log(d * ln2) / ln2;
                total += 2.0 * k + 2.0 / ln2;
            }
        }

        System.out.printf("%.7f\n", total);
    }
}
