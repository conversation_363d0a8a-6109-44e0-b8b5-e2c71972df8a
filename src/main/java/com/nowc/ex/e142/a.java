package com.nowc.ex.e142;

import java.io.*;
import java.util.Arrays;

public class a {

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));
        while (in.nextToken() != StreamTokenizer.TT_EOF) {
            int n = (int) in.nval;
            int[] arr = new int[n];
            int ans = 0;
            for (int i = 0, x, carry; i < n; i++) {
                carry = 1;
                in.nextToken();
                x = (int) in.nval;
                if (x == 0) {
                    ans += 8;
                }
                while (x > 0) {
                    if (x % 10 == 0) {
                        ans += 8 * carry;
                    }
                    x /= 10;
                    carry++;
                }
            }
            out.println(ans);
        }
        out.flush();
        out.close();
        br.close();
    }
}