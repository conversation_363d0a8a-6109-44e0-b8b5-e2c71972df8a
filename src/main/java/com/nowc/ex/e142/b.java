package com.nowc.ex.e142;

import java.io.*;
import java.util.HashMap;
import java.util.Map;

public class b {

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));

        in.nextToken();
        long n = (long) in.nval;

        Map<Long, Integer> map = new HashMap<>();
        while (true) {
            if (n == g(f(n))) {
                out.println(n);
                break;
            }
            n = g(f(n));
            if (map.containsKey(n)) {
                out.println(-1);
                break;
            } else {
                map.put(n, 1);
            }
        }
        out.flush();
        out.close();
        br.close();
    }

    private static long f(long x) {
        return Long.bitCount(x);
    }

    private static long g(long x) {
        return (64 - Long.numberOfLeadingZeros(x)) - f(x) + 1;
    }
}