package com.nowc.ex.e142;

import java.io.*;
import java.util.*;

public class g {

    static int[] p;
    static int check;

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));

        in.nextToken();
        int n = (int) in.nval;
        p = new int[n];
        check = inv(p);
        List<List<Integer>> ans = new ArrayList<>();
        for (int i = 0; i < n; i++) {
            in.nextToken();
            p[i] = (int) in.nval;
        }
        dfs(p, 0, ans);
        ans.sort((list1, list2) -> {
            int len1 = list1.size();
            int len2 = list2.size();
            int len = Math.min(len1, len2);
            for (int i = 0; i < len; i++) {
                if (!list1.get(i).equals(list2.get(i))) {
                    return list1.get(i) - list2.get(i);
                }
            }
            // 前面都相等，短的排前面
            return len1 - len2;
        });
        if (ans.isEmpty()) {
            out.println(-1);
        } else {
            for (int i = 0; i < ans.get(0).size(); i++) {
                out.print(ans.get(0).get(i) + " ");
            }
        }
        out.flush();
        out.close();
        br.close();
    }

    public static int inv(int[] arr) {
        return mergeSort(arr, 0, arr.length - 1, new int[arr.length]);
    }

    private static int mergeSort(int[] arr, int left, int right, int[] temp) {
        if (left >= right) return 0;
        int mid = (left + right) / 2;
        int count = mergeSort(arr, left, mid, temp)
                + mergeSort(arr, mid + 1, right, temp);

        int i = left, j = mid + 1, k = left;
        while (i <= mid && j <= right) {
            if (arr[i] <= arr[j]) {
                temp[k++] = arr[i++];
            } else {
                temp[k++] = arr[j++];
                count += (mid - i + 1); // 核心：左半段还有多少个比右半段的大
            }
        }
        while (i <= mid) temp[k++] = arr[i++];
        while (j <= right) temp[k++] = arr[j++];
        for (int l = left; l <= right; l++) arr[l] = temp[l];

        return count;
    }


    public static void dfs(int[] arr, int i, List<List<Integer>> ans) {
        if (i == arr.length) {
            if (!Arrays.equals(arr, p) && inv(arr) == check) {
                List<Integer> tmp = new ArrayList<>();
                for (int a : arr) {
                    tmp.add(a);
                }
                ans.add(tmp);
            }
        } else {
            HashSet<Integer> set = new HashSet<>();
            for (int j = i; j < arr.length; j++) {
                if (!set.contains(arr[j])) {
                    set.add(arr[j]);
                    swap(arr, i, j);
                    dfs(arr, i + 1, ans);
                    swap(arr, i, j);
                }
            }
        }
    }

    public static void swap(int[] arr, int i,int j) {
        int tmp = arr[i];
        arr[i] = arr[j];
        arr[j] = tmp;
    }
}