package com.nowc.month.m119;

import java.io.*;
import java.util.*;

public class d {

    static int MAXN = 200_000;
    // 链式前向星建图
    static int[] head = new int[MAXN + 5];
    static int[] to = new int[2 * MAXN + 5];
    static int[] nxt = new int[2 * MAXN + 5];
    static int edgeCnt;

    public static void addEdge(int u, int v) {
        to[edgeCnt] = v;
        nxt[edgeCnt] = head[u];
        head[u] = edgeCnt++;
    }


    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));
        in.nextToken(); int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken(); int n = (int) in.nval;
            Arrays.fill(head, 1, n + 1, -1);
            edgeCnt = 0;

            int[] indegree = new int[n + 1];
            for (int i = 1; i < n; i++) {
                in.nextToken(); int u = (int) in.nval;
                in.nextToken(); int v = (int) in.nval;
                addEdge(u, v);
                addEdge(v, u);
                indegree[u]++;
                indegree[v]++;
            }

            // 多源BFS：所有叶子节点（Sekai点）入队
            int[] dist = new int[n + 1];
            Arrays.fill(dist, -1);
            Deque<Integer> queue = new ArrayDeque<>();
            for (int i = 1; i <= n; i++) {
                if (indegree[i] == 1) {
                    dist[i] = 0;
                    queue.offer(i);
                }
            }

            // BFS计算到最近叶子的距离
            while (!queue.isEmpty()) {
                int u = queue.poll();
                for (int ei = head[u]; ei != -1; ei = nxt[ei]) {
                    int v = to[ei];
                    if (dist[v] == -1) {
                        dist[v] = dist[u] + 1;
                        queue.offer(v);
                    }
                }
            }

            // 寻找内部节点中距离最大值
            int maxD = -1;
            for (int i = 1; i <= n; i++) {
                if (indegree[i] > 1) {
                    maxD = Math.max(maxD, dist[i]);
                }
            }

            List<Integer> ans = new ArrayList<>();
            for (int i = 1; i <= n; i++) {
                if (indegree[i] > 1 && dist[i] == maxD) {
                    ans.add(i);
                }
            }
            Collections.sort(ans);

            out.println(ans.size());
            for (int a : ans) {
                out.print(a + " ");
            }
            out.println();
        }
        out.flush();
        out.close();
        br.close();
    }
}