package com.nowc.month.m119;

import java.io.*;
import java.util.HashMap;
import java.util.Map;

public class b {

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));
        in.nextToken(); int t = (int) in.nval;
        while (t-- > 0) {
            int[] arr = new int[5];
            Map<Integer, Integer> map = new HashMap<>();
            int zerocount = 0;
            for (int i = 0; i < 5; i++) {
                in.nextToken();
                int x = (int) in.nval;
                if (x == 0) {
                    zerocount++;
                    continue;
                }
                map.put(x, map.getOrDefault(x, 0) + 1);
            }
            in.nextToken();
            int k = (int) in.nval;
            if (zerocount == 5) {
                out.println("1/1000");
                continue;
            }
            if (map.containsKey(k)) {
                int count = map.get(k);
                int j = 5 - zerocount;
                if (j % count == 0) {
                    int gcd = gcd(count, j);
                    count /= gcd;
                    j /= gcd;
                }
                out.println(count + "/" + j);
            } else {
                out.println("0/1");
            }
        }
        out.flush();
        out.close();
        br.close();
    }

    public static int gcd(int a, int b) {
        return b == 0 ? a : gcd(b, a % b);
    }
}