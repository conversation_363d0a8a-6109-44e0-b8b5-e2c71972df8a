package com.nowc.month.m119;

import java.io.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class e {

    static final int MAXN = 200_000;
    // divs[x] 存放 x 的所有正约数
    static List<Integer>[] divs = new List[MAXN + 1];
    static {
        for (int i = 1; i <= MAXN; i++) {
            for (int j = i; j <= MAXN; j += i) {
                if (divs[j] == null) divs[j] = new ArrayList<Integer>();
                divs[j].add(i);
            }
        }
    }

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));
        in.nextToken();
        int t = (int) in.nval;
        int[] best = new int[MAXN + 1];
        while (t-- > 0) {
            in.nextToken();
            int n = (int) in.nval;
            int[] arr = new int[n];
            int v = 0;
            for (int i = 0; i < n; i++) {
                in.nextToken();
                arr[i] = (int) in.nval;
                v = Math.max(v, arr[i]);
            }
            for (int x = 1; x <= v; x++) {
                best[x] = 0;
            }

            int ans = 0;
            for (int x : arr) {
                int cand = 0;
                for (int d : divs[x]) {
                    cand = Math.max(cand, best[d]);
                }
                for (int m = x; m <= v; m += x) {
                    cand = Math.max(cand, best[m]);
                }
                int dp = cand + 1;
                best[x] = Math.max(best[x], dp);
                ans = Math.max(ans, dp);
            }
            out.println(ans);
        }
        out.flush();
        out.close();
        br.close();
    }
}