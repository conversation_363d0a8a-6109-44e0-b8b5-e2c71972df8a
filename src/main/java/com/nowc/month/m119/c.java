package com.nowc.month.m119;

import java.io.*;
import java.util.HashMap;
import java.util.Map;

public class c {

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));
        in.nextToken(); int t = (int) in.nval;
        while (t-- > 0) {
            in.nextToken();
            int n = (int) in.nval;
            int[] arr = new int[n];
            int max = Integer.MIN_VALUE;
            for (int i = 0; i < n; i++) {
                in.nextToken();
                arr[i] = (int) in.nval;
                max = Math.max(max, arr[i]);
            }
            if (2 * (max + 1) - 1 > n) {
                out.println("Lie");
            } else {
                out.println("Other");
            }
        }
        out.flush();
        out.close();
        br.close();
    }
}