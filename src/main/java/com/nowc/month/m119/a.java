package com.nowc.month.m119;

import java.io.*;
import java.util.Arrays;

public class a {

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        StreamTokenizer in = new StreamTokenizer(br);
        PrintWriter out = new PrintWriter(new OutputStreamWriter(System.out));
        while (in.nextToken() != StreamTokenizer.TT_EOF) {
            int[] arra = new int[5];
            int x = (int) in.nval;
            arra[0] = x;
            for (int i = 1; i < 5; i++) {
                in.nextToken();
                arra[i] = (int) in.nval;
            }
            int[] arrb = new int[5];
            for (int i = 0; i < 5; i++) {
                in.nextToken();
                arrb[i] = (int) in.nval;
            }

            Arrays.sort(arra);
            Arrays.sort(arrb);
            int asum = arra[0];
            int bsum = arrb[0] * 2;
            for (int i = 1; i < 4; i++) {
                asum += arra[i];
                bsum += arrb[i];
            }
            asum += arra[4] * 2;
            bsum += arrb[4];
            if (asum > bsum) {
                out.println("YES");
            } else {
                out.println("NO");
            }
        }
        out.flush();
        out.close();
        br.close();
    }
}